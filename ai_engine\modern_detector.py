#!/usr/bin/env python3
"""
Modern AI Text Detection Engine
Enhanced with latest OpenAI API, better error handling, and comprehensive analytics
"""

import sys
import os
import logging
import re
import json
import time
import hashlib
from datetime import datetime
from dataclasses import dataclass, asdict
from typing import Tuple, Dict, Optional, List
from dotenv import load_dotenv
from openai import OpenAI
import random

# Load environment variables
load_dotenv()

@dataclass
class AnalysisResult:
    """Data class for analysis results"""
    classification: str
    confidence: float
    sub_scores: Dict[str, float]
    reasoning: str
    processing_time: float
    model_used: str
    tokens_used: int = 0
    cost_estimate: float = 0.0
    session_id: str = ""
    
    def to_dict(self):
        return asdict(self)

class ModernAITextDetector:
    """
    Modern AI Text Detection Engine
    Enhanced with better error handling, caching, and analytics
    """
    
    def __init__(self):
        # Initialize OpenAI client
        self.api_key = os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("Error: No API key provided. Please set the OPENAI_API_KEY environment variable.")
        
        self.client = OpenAI(api_key=self.api_key)
        self.model = os.getenv("OPENAI_MODEL", "gpt-4o-mini")
        self.max_tokens = int(os.getenv("MAX_TOKENS", 1500))
        self.temperature = float(os.getenv("TEMPERATURE", 0.3))
        
        # Setup logging
        self.setup_logging()
        
        # Cache for repeated analyses
        self.analysis_cache = {}
        
        # Cost tracking (approximate)
        self.cost_per_token = {
            "gpt-4o": 0.00003,
            "gpt-4o-mini": 0.00000015,
            "gpt-4": 0.00003,
            "gpt-3.5-turbo": 0.000002
        }
        
    def setup_logging(self):
        """Setup enhanced logging system"""
        # Create logs directory if it doesn't exist
        os.makedirs('logs', exist_ok=True)
        
        # Configure loggers
        self.logger_analysis = logging.getLogger('analysis')
        self.logger_error = logging.getLogger('error')
        self.logger_performance = logging.getLogger('performance')
        
        # Set levels
        self.logger_analysis.setLevel(logging.INFO)
        self.logger_error.setLevel(logging.ERROR)
        self.logger_performance.setLevel(logging.INFO)
        
        # Create handlers
        analysis_handler = logging.FileHandler('logs/analysis.log', mode='a')
        error_handler = logging.FileHandler('logs/error.log', mode='a')
        performance_handler = logging.FileHandler('logs/performance.log', mode='a')
        
        # Create formatters
        formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
        analysis_handler.setFormatter(formatter)
        error_handler.setFormatter(formatter)
        performance_handler.setFormatter(formatter)
        
        # Add handlers to loggers
        self.logger_analysis.addHandler(analysis_handler)
        self.logger_error.addHandler(error_handler)
        self.logger_performance.addHandler(performance_handler)
    
    def calculate_dynamic_confidence(self, base_score: float, factual_bias: bool = False) -> float:
        """
        Introduces variability in the confidence score to simulate uncertainty in classification.
        Adjusts confidence for factual and professional writing (journalism bias).
        """
        variability = random.uniform(-0.1, 0.1)  # ±10% variability
        boost = 0.05 if factual_bias else 0.0  # Boost for professional/journalism text
        dynamic_score = max(0.1, min(base_score + variability + boost, 1.0))
        return round(dynamic_score * 100, 2)
    
    def get_text_hash(self, text: str) -> str:
        """Generate hash for text caching"""
        return hashlib.md5(text.encode()).hexdigest()
    
    def is_cached(self, text: str) -> Optional[AnalysisResult]:
        """Check if analysis is cached"""
        text_hash = self.get_text_hash(text)
        return self.analysis_cache.get(text_hash)
    
    def cache_result(self, text: str, result: AnalysisResult):
        """Cache analysis result"""
        text_hash = self.get_text_hash(text)
        self.analysis_cache[text_hash] = result
        
        # Limit cache size
        if len(self.analysis_cache) > 1000:
            oldest_key = next(iter(self.analysis_cache))
            del self.analysis_cache[oldest_key]
    
    def parse_ai_response(self, ai_response: str) -> Tuple[Optional[str], Optional[float], Dict[str, float], str]:
        """
        Parses the AI's response to extract classification, confidence, sub-scores, and reasoning.
        """
        label = None
        confidence = None
        sub_scores = {}
        reasoning = ai_response
        
        # Extract classification
        classification_match = re.search(
            r"\*\*(?:Label|Classification)(?:\s*of\s*Text)?\*\*\s*:\s*(.*?)(?:\s+\(Confidence:\s*([\d\.]+)%\))?", 
            ai_response, 
            re.IGNORECASE
        )
        
        if classification_match:
            classification = classification_match.group(1).strip().lower()
            confidence_value = classification_match.group(2)
            
            if "ai-generated" in classification or "ai generated" in classification:
                label = "AI-Generated"
            elif "human-written" in classification or "human written" in classification:
                label = "Human-Written"
            elif "mixed" in classification:
                label = "Mixed"
            
            # Calculate dynamic confidence
            base_conf = float(confidence_value) / 100 if confidence_value else 0.85
            factual_bias = "factual" in ai_response.lower() or "professional" in ai_response.lower()
            confidence = self.calculate_dynamic_confidence(base_conf, factual_bias)
        else:
            # Default classification
            label = "Mixed"
            confidence = self.calculate_dynamic_confidence(0.7)
        
        # Extract sub-scores
        score_patterns = [
            "Creativity",
            "Authenticity", 
            "Language",
            "Factual Accuracy",
            "Emotional Expression"
        ]
        
        for score_name in score_patterns:
            pattern = rf"{re.escape(score_name)}[^:]*:\s*([0-5](?:\.\d+)?)/5"
            score_match = re.search(pattern, ai_response, re.IGNORECASE)
            if score_match:
                sub_scores[score_name] = float(score_match.group(1))
        
        return label, confidence, sub_scores, reasoning
    
    def is_abstract(self, text: str) -> bool:
        """
        Detects if the given text resembles an abstract or summary.
        """
        word_count = len(text.split())
        has_abstract_keywords = bool(re.search(r"(abstract|summary|overview|introduction)", text, re.IGNORECASE))
        
        return word_count < 150 and has_abstract_keywords
    
    def estimate_cost(self, tokens_used: int) -> float:
        """Estimate API cost based on tokens used"""
        cost_per_token = self.cost_per_token.get(self.model, 0.00001)
        return tokens_used * cost_per_token
    
    def analyze_text(self, text: str, session_id: str = "") -> AnalysisResult:
        """
        Main analysis function with comprehensive error handling and performance tracking
        """
        start_time = time.time()
        
        try:
            # Input validation
            if not text or not text.strip():
                raise ValueError("Input text is empty or invalid.")
            
            # Check cache first
            cached_result = self.is_cached(text)
            if cached_result:
                self.logger_performance.info(f"Cache hit for text hash: {self.get_text_hash(text)}")
                return cached_result
            
            # Check for abstract
            if self.is_abstract(text):
                result = AnalysisResult(
                    classification="Abstract",
                    confidence=100.0,
                    sub_scores={},
                    reasoning="This text appears to be an abstract or summary.",
                    processing_time=time.time() - start_time,
                    model_used=self.model,
                    session_id=session_id
                )
                self.cache_result(text, result)
                return result
            
            # Enhanced prompt for better analysis
            system_prompt = self.get_enhanced_prompt()
            
            # Make API call
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Analyze this text:\n\n{text}"}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            
            ai_response = response.choices[0].message.content.strip()
            tokens_used = response.usage.total_tokens if response.usage else 0
            
            # Parse response
            label, confidence, sub_scores, reasoning = self.parse_ai_response(ai_response)
            
            # Create result
            processing_time = time.time() - start_time
            result = AnalysisResult(
                classification=label or "Unknown",
                confidence=confidence or 0.0,
                sub_scores=sub_scores,
                reasoning=reasoning,
                processing_time=processing_time,
                model_used=self.model,
                tokens_used=tokens_used,
                cost_estimate=self.estimate_cost(tokens_used),
                session_id=session_id
            )
            
            # Cache and log
            self.cache_result(text, result)
            self.log_analysis(text, result)
            
            return result
            
        except Exception as e:
            self.logger_error.error(f"Analysis error: {str(e)}", exc_info=True)
            processing_time = time.time() - start_time
            
            return AnalysisResult(
                classification="Error",
                confidence=0.0,
                sub_scores={},
                reasoning=f"An error occurred during analysis: {str(e)}",
                processing_time=processing_time,
                model_used=self.model,
                session_id=session_id
            )
    
    def get_enhanced_prompt(self) -> str:
        """Get the enhanced analysis prompt"""
        return """You are an advanced AI text detection system. Analyze the provided text to determine if it's AI-generated, human-written, or mixed.

Use these criteria:
1. **Language Patterns**: Look for overly formal, repetitive, or unnatural phrasing
2. **Emotional Authenticity**: Assess genuine emotional expression vs artificial sentiment
3. **Cultural Context**: Check for appropriate cultural references and nuances
4. **Factual Accuracy**: Verify claims and check for hallucinations
5. **Creative Elements**: Evaluate originality, humor, and unique perspectives

Provide your analysis in this format:
**Classification**: [AI-Generated/Human-Written/Mixed] (Confidence: X%)

**Sub-Scores**:
- Creativity: X/5
- Authenticity: X/5  
- Language: X/5
- Factual Accuracy: X/5
- Emotional Expression: X/5

**Reasoning**: [Detailed explanation of your analysis]

Remember: Professional, factual writing isn't automatically AI-generated. Consider context and domain expertise."""
    
    def log_analysis(self, text: str, result: AnalysisResult):
        """Log analysis details"""
        log_data = {
            "text_length": len(text),
            "classification": result.classification,
            "confidence": result.confidence,
            "processing_time": result.processing_time,
            "model_used": result.model_used,
            "tokens_used": result.tokens_used,
            "cost_estimate": result.cost_estimate,
            "session_id": result.session_id
        }
        
        self.logger_analysis.info(json.dumps(log_data))
        self.logger_performance.info(f"Analysis completed in {result.processing_time:.2f}s")

# Legacy function for backward compatibility
def is_generated_by_ai(paragraph: str, model: str = "gpt-4o-mini") -> Tuple[Optional[str], Optional[float], Dict[str, float], str]:
    """Legacy function for backward compatibility"""
    detector = ModernAITextDetector()
    result = detector.analyze_text(paragraph)
    return result.classification, result.confidence, result.sub_scores, result.reasoning

# Command line interface
if __name__ == "__main__":
    if len(sys.argv) > 1:
        input_text = ' '.join(sys.argv[1:])
        detector = ModernAITextDetector()
        result = detector.analyze_text(input_text)
        
        if result.classification == "Error":
            print("An error occurred during the detection process.")
        else:
            print(result.reasoning)
            if result.sub_scores:
                print("\nHuman Content Sub-Scores:")
                for score, value in result.sub_scores.items():
                    print(f"{score}: {value}")
    else:
        print("Usage: Provide a paragraph as an argument.")
