<?php
/**
 * AI Text Detector Service - Modern Interface
 * Updated with modern design and improved functionality
 */

session_start();

// Check authentication
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}

require_once 'db.php';
require_once 'ai_analysis_service.php';

// Handle analysis request
$result = null;
$error = null;
$feedback_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Handle feedback submission
    if (isset($_POST['feedback'])) {
        $feedback_text = trim($_POST['feedback']);
        if (!empty($feedback_text)) {
            try {
                $stmt = $conn->prepare("INSERT INTO feedback (feedback_text) VALUES (?)");
                $stmt->bind_param("s", $feedback_text);
                $stmt->execute();
                $feedback_message = "Thank you for your feedback!";
                $stmt->close();
            } catch (Exception $e) {
                $feedback_message = "Failed to submit feedback: " . $e->getMessage();
            }
        } else {
            $feedback_message = "Feedback cannot be empty.";
        }
    }

    // Handle analysis request
    if (isset($_POST['analyze'])) {
        $inputText = trim($_POST['text'] ?? '');

        if (empty($inputText)) {
            $error = "Please enter some text to analyze.";
        } else {
            // Word count check
            $wordCount = str_word_count($inputText);
            if ($wordCount > 2000) {
                $error = "Text exceeds maximum limit of 2000 words. Current: $wordCount words.";
            } else {
                // Use the AI Analysis Service
                try {
                    $aiService = new AIAnalysisService();
                    $sessionId = session_id();
                    $analysisResult = $aiService->analyzeText($inputText, $sessionId);

                    if ($analysisResult['success']) {
                        $result = $analysisResult;
                    } else {
                        $error = "Analysis failed: " . ($analysisResult['message'] ?? 'Unknown error');
                    }
                } catch (Exception $e) {
                    $error = "Analysis service error: " . $e->getMessage();
                }
            }
        }
    }

}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Text Detector - Analysis Service</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        /* Dark Mode Styles */
        body.dark-mode {
            background: #0f172a;
            color: #e2e8f0;
        }

        body.dark-mode .header {
            background: #1e293b;
            border-bottom-color: #334155;
        }

        body.dark-mode .logo {
            color: #e2e8f0;
        }

        body.dark-mode .nav-link {
            color: #94a3b8;
        }

        body.dark-mode .nav-link:hover,
        body.dark-mode .nav-link.active {
            color: #60a5fa;
        }

        body.dark-mode .user-avatar {
            background: #334155;
            color: #94a3b8;
        }

        body.dark-mode .btn-secondary {
            background: #334155;
            color: #e2e8f0;
        }

        body.dark-mode .btn-secondary:hover {
            background: #475569;
        }

        body.dark-mode .analysis-section,
        body.dark-mode .results-section {
            background: #1e293b;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
        }

        body.dark-mode .section-header {
            background: #334155;
            border-bottom-color: #475569;
        }

        body.dark-mode .section-title {
            color: #e2e8f0;
        }

        body.dark-mode .section-description {
            color: #94a3b8;
        }

        body.dark-mode .form-textarea {
            background: #334155;
            border-color: #475569;
            color: #e2e8f0;
        }

        body.dark-mode .form-textarea:focus {
            border-color: #60a5fa;
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
        }

        body.dark-mode .form-label {
            color: #e2e8f0;
        }

        body.dark-mode .word-counter {
            color: #94a3b8;
        }

        body.dark-mode .score-item {
            background: #334155;
            border-color: #475569;
        }

        body.dark-mode .score-label {
            color: #94a3b8;
        }

        body.dark-mode .score-value {
            color: #e2e8f0;
        }

        body.dark-mode .reasoning-section {
            background: #334155;
            border-color: #475569;
        }

        body.dark-mode .reasoning-title {
            color: #e2e8f0;
        }

        body.dark-mode .reasoning-content {
            color: #cbd5e1;
        }

        body.dark-mode .placeholder {
            color: #94a3b8;
        }

        body.dark-mode .error-message {
            background: #7f1d1d;
            border-color: #dc2626;
            color: #fecaca;
        }

        body.dark-mode .success-message {
            background: #14532d;
            border-color: #16a34a;
            color: #bbf7d0;
        }

        /* Header */
        .header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 700;
            font-size: 1.25rem;
            color: #1e293b;
        }

        .logo-icon {
            width: 2.5rem;
            height: 2.5rem;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .nav-link {
            color: #64748b;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s;
        }

        .nav-link:hover, .nav-link.active {
            color: #3b82f6;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-avatar {
            width: 2rem;
            height: 2rem;
            background: #e2e8f0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #64748b;
        }

        /* Main Content */
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .hero-section {
            text-align: center;
            margin-bottom: 3rem;
        }

        .hero-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 1rem;
        }

        .hero-subtitle {
            font-size: 1.125rem;
            color: #64748b;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Analysis Section */
        .analysis-section {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .section-header {
            background: #f8fafc;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        .section-description {
            color: #64748b;
            font-size: 0.875rem;
        }

        .section-content {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        .form-textarea {
            width: 100%;
            min-height: 200px;
            padding: 1rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            font-size: 1rem;
            font-family: inherit;
            resize: vertical;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1rem;
        }

        .word-counter {
            font-size: 0.875rem;
            color: #64748b;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 500;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        /* Dark Mode Toggle */
        .dark-mode-toggle {
            background: none;
            border: 2px solid #e2e8f0;
            border-radius: 50%;
            width: 2.5rem;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #64748b;
            margin-right: 1rem;
        }

        .dark-mode-toggle:hover {
            border-color: #3b82f6;
            color: #3b82f6;
            transform: rotate(180deg);
        }

        body.dark-mode .dark-mode-toggle {
            border-color: #475569;
            color: #94a3b8;
        }

        body.dark-mode .dark-mode-toggle:hover {
            border-color: #60a5fa;
            color: #60a5fa;
        }

        /* Results Section */
        .results-section {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .result-classification {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: 500;
            font-size: 0.875rem;
        }

        .result-classification.ai-generated {
            background: #fef3c7;
            color: #92400e;
        }

        .result-classification.human-written {
            background: #d1fae5;
            color: #065f46;
        }

        .confidence-display {
            text-align: right;
        }

        .confidence-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1e293b;
        }

        .confidence-label {
            font-size: 0.875rem;
            color: #64748b;
        }

        .confidence-meter {
            margin: 1.5rem 0;
        }

        .meter-bar {
            height: 0.5rem;
            background: #f1f5f9;
            border-radius: 0.25rem;
            overflow: hidden;
        }

        .meter-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            border-radius: 0.25rem;
            transition: width 1s ease-out;
        }

        .sub-scores {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .score-item {
            background: #f8fafc;
            padding: 1rem;
            border-radius: 0.5rem;
            border: 1px solid #e2e8f0;
        }

        .score-label {
            font-size: 0.875rem;
            color: #64748b;
            margin-bottom: 0.5rem;
        }

        .score-value {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
        }

        .reasoning-section {
            background: #f8fafc;
            padding: 1.5rem;
            border-radius: 0.5rem;
            border: 1px solid #e2e8f0;
            margin-top: 1.5rem;
        }

        .reasoning-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 0.75rem;
        }

        .reasoning-content {
            color: #475569;
            line-height: 1.6;
        }

        .error-message {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }

        .success-message {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #166534;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }

        .placeholder {
            text-align: center;
            padding: 3rem 2rem;
            color: #64748b;
        }

        .placeholder i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-container {
                padding: 0 1rem;
            }

            .nav-menu {
                display: none;
            }

            .main-container {
                padding: 1rem;
            }

            .hero-title {
                font-size: 2rem;
            }

            .section-content {
                padding: 1.5rem;
            }

            .sub-scores {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="logo">
                <div class="logo-icon">
                    <i class="fas fa-brain"></i>
                </div>
                AI Text Detector
            </div>

            <nav class="nav-menu">
                <a href="home.php" class="nav-link">Home</a>
                <a href="service.php" class="nav-link active">Analyze</a>
                <a href="aboutus.php" class="nav-link">About</a>
            </nav>

            <div class="user-menu">
                <button class="dark-mode-toggle" onclick="toggleDarkMode()" title="Toggle Dark Mode">
                    <i class="fas fa-moon" id="dark-mode-icon"></i>
                </button>
                <span>Welcome, <?= htmlspecialchars($_SESSION['username']) ?></span>
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-sign-out-alt"></i>
                    Sign Out
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-container">
        <!-- Hero Section -->
        <div class="hero-section">
            <h1 class="hero-title">AI Text Detection</h1>
            <p class="hero-subtitle">
                Analyze your text to determine if it was written by AI or humans using advanced machine learning algorithms.
            </p>
        </div>

        <!-- Analysis Section -->
        <div class="analysis-section">
            <div class="section-header">
                <h2 class="section-title">Text Analysis</h2>
                <p class="section-description">Enter or paste the text you want to analyze below</p>
            </div>

            <div class="section-content">
                <?php if ($error): ?>
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <form method="post">
                    <div class="form-group">
                        <label for="text" class="form-label">Text to Analyze</label>
                        <textarea
                            id="text"
                            name="text"
                            class="form-textarea"
                            placeholder="Paste your text here for analysis... (Maximum 2000 words)"
                            required
                        ><?= htmlspecialchars($_POST['text'] ?? '') ?></textarea>

                        <div class="form-footer">
                            <div class="word-counter">
                                <span id="word-count">0</span> / 2000 words
                            </div>
                            <button type="submit" name="analyze" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                                Analyze Text
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Results Section -->
        <div class="results-section">
            <div class="section-header">
                <h2 class="section-title">Analysis Results</h2>
                <p class="section-description">Detailed breakdown of the text analysis</p>
            </div>

            <div class="section-content">
                <?php if ($result): ?>
                    <div class="result-header">
                        <div class="result-classification <?= strtolower(str_replace([' ', '-'], '_', $result['classification'])) ?>">
                            <i class="fas fa-<?= $result['classification'] === 'AI-Generated' ? 'robot' : 'user' ?>"></i>
                            <?= htmlspecialchars($result['classification']) ?>
                        </div>

                        <div class="confidence-display">
                            <div class="confidence-value"><?= $result['confidence'] ?>%</div>
                            <div class="confidence-label">Confidence</div>
                        </div>
                    </div>

                    <div class="confidence-meter">
                        <div class="meter-bar">
                            <div class="meter-fill" style="width: <?= $result['confidence'] ?>%"></div>
                        </div>
                    </div>

                    <?php if (!empty($result['sub_scores'])): ?>
                        <div class="sub-scores">
                            <?php foreach ($result['sub_scores'] as $metric => $score): ?>
                                <div class="score-item">
                                    <div class="score-label"><?= htmlspecialchars($metric) ?></div>
                                    <div class="score-value"><?= $score ?>/5</div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                    <div class="reasoning-section">
                        <h3 class="reasoning-title">Analysis Explanation</h3>
                        <div class="reasoning-content">
                            <?= nl2br(htmlspecialchars($result['reasoning'])) ?>
                        </div>
                    </div>

                    <div style="margin-top: 1.5rem; padding-top: 1.5rem; border-top: 1px solid #e2e8f0; font-size: 0.875rem; color: #64748b;">
                        <i class="fas fa-clock"></i> Processing time: <?= $result['processing_time'] ?>ms
                        <span style="margin-left: 1rem;"><i class="fas fa-font"></i> Word count: <?= $result['word_count'] ?></span>
                    </div>
                <?php else: ?>
                    <div class="placeholder">
                        <i class="fas fa-search"></i>
                        <h3>Ready for Analysis</h3>
                        <p>Enter text above to begin AI detection analysis</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Feedback Section -->
        <div class="analysis-section" style="margin-top: 2rem;">
            <div class="section-header">
                <h2 class="section-title">Provide Feedback</h2>
                <p class="section-description">Help us improve our analysis accuracy</p>
            </div>

            <div class="section-content">
                <?php if ($feedback_message): ?>
                    <div class="success-message">
                        <i class="fas fa-check-circle"></i>
                        <?= htmlspecialchars($feedback_message) ?>
                    </div>
                <?php endif; ?>

                <form method="post">
                    <div class="form-group">
                        <label for="feedback" class="form-label">Your Feedback</label>
                        <textarea
                            id="feedback"
                            name="feedback"
                            class="form-textarea"
                            placeholder="Share your thoughts about the analysis accuracy or suggest improvements..."
                            rows="4"
                            style="min-height: 120px;"
                        ></textarea>

                        <div class="form-footer">
                            <div></div>
                            <button type="submit" class="btn btn-secondary">
                                <i class="fas fa-comment"></i>
                                Submit Feedback
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <script>
        // Dark Mode Functionality
        function toggleDarkMode() {
            const body = document.body;
            const icon = document.getElementById('dark-mode-icon');

            body.classList.toggle('dark-mode');

            // Update icon
            if (body.classList.contains('dark-mode')) {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
            }

            // Save preference
            localStorage.setItem('darkMode', body.classList.contains('dark-mode'));
        }

        // Load dark mode preference
        window.addEventListener('load', function() {
            const darkMode = localStorage.getItem('darkMode') === 'true';
            const body = document.body;
            const icon = document.getElementById('dark-mode-icon');

            if (darkMode) {
                body.classList.add('dark-mode');
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            }
        });

        // Word counter
        const textArea = document.getElementById('text');
        const wordCountDisplay = document.getElementById('word-count');

        function updateWordCount() {
            const text = textArea.value.trim();
            const words = text ? text.split(/\s+/).length : 0;
            wordCountDisplay.textContent = words;

            // Update button state
            const analyzeBtn = document.querySelector('button[name="analyze"]');
            analyzeBtn.disabled = words === 0 || words > 2000;

            // Update counter color based on theme
            const isDark = document.body.classList.contains('dark-mode');
            if (words > 2000) {
                wordCountDisplay.style.color = '#dc2626';
            } else if (words > 1800) {
                wordCountDisplay.style.color = '#f59e0b';
            } else {
                wordCountDisplay.style.color = isDark ? '#94a3b8' : '#64748b';
            }
        }

        textArea.addEventListener('input', updateWordCount);

        // Initialize word count
        updateWordCount();

        // Animate confidence meter on load
        window.addEventListener('load', function() {
            const meterFill = document.querySelector('.meter-fill');
            if (meterFill) {
                const width = meterFill.style.width;
                meterFill.style.width = '0%';
                setTimeout(() => {
                    meterFill.style.width = width;
                }, 500);
            }
        });
    </script>
</body>
</html>
