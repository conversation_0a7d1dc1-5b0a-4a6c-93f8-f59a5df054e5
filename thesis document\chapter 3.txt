CHAPTER III
RESEARCH METHODOLOGY

This chapter presents the comprehensive methodology employed to develop and evaluate an AI-generated text detection system for digital content verification. The methodology encompasses the research design framework, data collection and preprocessing procedures, feature engineering approaches, machine learning algorithm implementation, ensemble learning methodology, and evaluation protocols. This systematic approach ensures the development of a robust, accurate, and practically deployable AI text detection system.

3.1 Research Design and Philosophical Framework

This investigation employs a mixed-methods research design that integrates quantitative experimental analysis with qualitative evaluation approaches to address the complex challenges of AI text detection. The research design is grounded in pragmatic philosophical foundations that emphasize empirical validation, practical utility, and evidence-based conclusions while acknowledging the interdisciplinary nature of the investigation.

The pragmatic approach enables the integration of diverse methodological approaches, including quantitative performance analysis, qualitative user evaluation, and mixed-methods validation procedures that collectively provide comprehensive assessment of detection system effectiveness. This philosophical foundation supports the development of solutions that balance theoretical rigor with practical applicability and user requirements.

The overall research design follows an experimental framework that systematically investigates the effectiveness of different detection approaches while building toward an optimized ensemble solution. The design incorporates four distinct phases of investigation, each addressing specific research questions while contributing to comprehensive understanding of AI text detection challenges and solutions.

3.2 Research Methodology Framework

This research employs a comprehensive quantitative methodology with qualitative validation components to develop and evaluate an AI text detection system. The quantitative approach enables systematic measurement of detection performance, statistical validation of results, and objective comparison of different algorithmic approaches. Qualitative components provide insights into user experience, practical utility, and real-world effectiveness.

3.2.1 Four-Phase Research Design

**Phase I: Data Collection and Preprocessing**
This phase establishes the foundation for system development through comprehensive data collection and preprocessing procedures. Activities include compilation of diverse text samples from multiple sources, systematic labeling and quality assurance procedures, and preprocessing to ensure data quality and representativeness across different content types and generation models.

**Phase II: Feature Engineering and Algorithm Development**
This phase focuses on developing comprehensive feature extraction frameworks and implementing individual machine learning algorithms. Activities include designing lexical, syntactic, semantic, and statistical feature extraction procedures, implementing and optimizing Random Forest, Support Vector Machine, and Neural Network algorithms, and conducting systematic hyperparameter optimization for each algorithm.

**Phase III: Ensemble System Development and Integration**
This phase addresses the core innovation of combining individual algorithms through sophisticated ensemble learning methodologies. Activities include developing ensemble architectures, optimizing combination strategies through weighted voting and meta-learning approaches, and conducting comprehensive performance evaluation of the integrated system.

**Phase IV: Validation and Real-World Testing**
This phase validates system effectiveness through rigorous testing and real-world deployment scenarios. Activities include cross-validation procedures, comparative analysis with existing methods, user studies and practical deployment testing, and assessment of system performance in authentic application contexts.
3.3 Experimental Design Principles

The experimental design incorporates rigorous scientific methodology principles to ensure reliable, valid, and reproducible results. These principles guide all aspects of the investigation from data collection through final evaluation.

**Controlled Experimentation:** Systematic assessment of different approaches while isolating the effects of specific variables and design decisions. This includes controlled comparison of individual algorithms, systematic evaluation of feature combinations, and isolated testing of ensemble strategies.

**Randomization Procedures:** Implementation of randomization at multiple levels to prevent bias and ensure generalizability. This includes random sampling of training and testing datasets, random initialization of machine learning algorithms, randomized cross-validation procedures, and randomized experimental conditions.

**Reproducibility and Replication:** Comprehensive documentation of all procedures to enable independent validation and replication. This includes detailed implementation specifications, complete parameter documentation, standardized evaluation procedures, and open-source code availability.
3.4 Data Collection and Dataset Development

3.4.1 Dataset Composition and Sources

The research requires a comprehensive, balanced dataset representing diverse text types and generation approaches. The dataset compilation strategy prioritizes diversity, quality, and representativeness while ensuring balanced coverage across different content categories and generation methods.

**Human-Authored Content Collection:**
- Academic publications: 2,000 samples from peer-reviewed journals across multiple disciplines
- News articles: 2,000 samples from reputable journalism sources and news organizations
- Professional content: 1,500 samples including business communications and technical documentation
- Creative writing: 1,000 samples including fiction, poetry, and artistic expressions
- Digital content: 1,000 samples from blogs, websites, and online publications

**AI-Generated Content Collection:**
- GPT-series models: 3,000 samples from GPT-3, GPT-3.5, and GPT-4 across various prompts
- Alternative models: 2,000 samples from BERT, T5, and other transformer architectures
- Specialized models: 1,500 samples from domain-specific and fine-tuned models
- Instruction-tuned models: 1,000 samples from ChatGPT and similar conversational AI
- Mixed-generation: 500 samples representing hybrid human-AI collaborative content

**Total Dataset: 15,000 balanced samples (7,500 human-authored, 7,500 AI-generated)**

3.4.2 Quality Assurance and Validation Procedures

**Authenticity Verification:**
- Source documentation and verification for all human-authored content
- Generation model confirmation and parameter documentation for AI content
- Plagiarism detection to ensure originality and prevent contamination
- Expert review for domain-specific content validation

**Content Quality Assessment:**
- Coherence evaluation using both automated metrics and human assessment
- Readability analysis using standard metrics (Flesch-Kincaid, SMOG Index)
- Length distribution analysis ensuring representative coverage (50-2000 words)
- Language quality verification through native speaker review

**Representativeness Validation:**
- Statistical analysis of content characteristics across categories
- Temporal distribution ensuring current and relevant content
- Domain coverage assessment across academic, professional, and creative contexts
- Demographic representation across different author backgrounds and perspectives

3.5 Feature Engineering Framework

The feature engineering framework captures multidimensional characteristics of text content across four primary categories, providing comprehensive representation for machine learning algorithms.

3.5.1 Lexical Features (12 features)

**Vocabulary Richness Measures:**
- Type-Token Ratio (TTR): Ratio of unique words to total words
- Moving Average Type-Token Ratio (MATTR): Length-independent lexical diversity
- Measure of Textual Lexical Diversity (MTLD): Advanced lexical diversity calculation
- Hapax Legomena Ratio: Frequency of words appearing only once

**Word Usage Patterns:**
- Average word length and syllable count distributions
- Vocabulary sophistication index based on word frequency rankings
- Semantic field coverage across different conceptual domains
- Word frequency distribution characteristics and Zipfian analysis

3.5.2 Syntactic Features (11 features)

**Sentence Structure Analysis:**
- Sentence length distribution and variance measures
- Clause complexity and subordination patterns
- Coordination usage frequency and patterns
- Sentence type distribution (declarative, interrogative, imperative)

**Grammatical Pattern Analysis:**
- Part-of-speech tag distribution and transition patterns
- Dependency parsing depth and branching factor analysis
- Grammatical complexity index based on syntactic tree structures
- Function word usage patterns and distributions

3.5.3 Semantic Features (10 features)

**Coherence and Consistency Measures:**
- Topic coherence using Latent Dirichlet Allocation (LDA)
- Semantic similarity between consecutive sentences using word embeddings
- Conceptual progression and thematic development indicators
- Factual consistency and knowledge coherence assessment

**Meaning Analysis:**
- Semantic density and information content measures
- Abstract vs. concrete concept ratio analysis
- Sentiment consistency and emotional coherence patterns
- Contextual appropriateness and pragmatic coherence indicators

3.5.4 Statistical Features (11 features)

**Information-Theoretic Measures:**
- Character-level entropy and information content
- Compression ratio using standard algorithms (gzip, bzip2)
- Algorithmic complexity estimation through compression
- Information density and redundancy measures

**Distribution Analysis:**
- Character and word frequency distribution patterns
- Punctuation usage frequency and pattern analysis
- Capitalization pattern consistency and distribution
- N-gram frequency distribution characteristics
3.6 Machine Learning Algorithm Implementation

3.6.1 Random Forest Implementation

**Algorithm Configuration:**
- Number of estimators: Optimized through grid search (100-1000 trees)
- Maximum depth: Systematically tuned to prevent overfitting
- Minimum samples split/leaf: Optimized for dataset characteristics
- Feature sampling: Square root and logarithmic strategies evaluated

**Optimization Procedures:**
- Hyperparameter tuning using 5-fold cross-validation
- Feature importance analysis for interpretability
- Out-of-bag error estimation for performance assessment
- Bootstrap sampling optimization for training diversity

3.6.2 Support Vector Machine Implementation

**Kernel Optimization:**
- Linear kernel for baseline performance assessment
- Radial Basis Function (RBF) kernel with gamma parameter tuning
- Polynomial kernel evaluation with degree optimization
- Custom kernel development for text-specific characteristics

**Parameter Optimization:**
- Regularization parameter (C) optimization through grid search
- Kernel parameter tuning using cross-validation procedures
- Probability calibration using Platt scaling for ensemble integration
- Class weight optimization for balanced classification

3.6.3 Neural Network Implementation

**Architecture Design:**
- Input layer: 44 features with standardization preprocessing
- Hidden layers: Systematic optimization of depth and width
- Activation functions: ReLU, tanh, and sigmoid evaluation
- Output layer: Softmax activation for probability estimation

**Training Optimization:**
- Learning rate scheduling with adaptive adjustment
- Dropout regularization to prevent overfitting
- Batch normalization for stable training
- Early stopping based on validation performance

**Advanced Techniques:**
- Weight initialization using Xavier/He methods
- Gradient clipping for stable convergence
- L1/L2 regularization for generalization improvement
- Cross-validation for architecture selection

3.7 Ensemble Learning Methodology

3.7.1 Ensemble Architecture Design

The ensemble learning approach combines the three optimized individual algorithms through sophisticated integration strategies that leverage their complementary strengths while mitigating individual weaknesses.

**Weighted Voting Implementation:**
- Static weight optimization based on cross-validation performance
- Dynamic weight adaptation based on input text characteristics
- Confidence-weighted voting incorporating prediction uncertainty
- Performance-based weight adjustment using ongoing validation results

**Meta-Learning Approach:**
- Secondary classifier training using base algorithm predictions
- Feature engineering for meta-learning including confidence scores
- Cross-validation procedures to prevent overfitting in meta-learning
- Meta-learner selection and optimization (logistic regression, neural networks)

3.7.2 Ensemble Optimization Procedures

**Weight Optimization:**
- Grid search across weight combinations for static voting
- Gradient-based optimization for dynamic weighting
- Cross-validation assessment of optimal weight configurations
- Statistical validation of weight selection procedures

**Performance Integration:**
- Prediction probability combination strategies
- Confidence score aggregation and calibration
- Uncertainty quantification for ensemble predictions
- Decision threshold optimization for classification tasks

3.8 Evaluation Framework and Metrics

3.8.1 Performance Metrics

**Primary Classification Metrics:**
- Accuracy: Overall correct classification rate
- Precision: True positive rate for each class (AI-generated, human-authored)
- Recall: Sensitivity for detecting each class
- F1-Score: Harmonic mean of precision and recall
- AUC-ROC: Area under receiver operating characteristic curve

**Advanced Evaluation Measures:**
- Cross-validation stability assessment (mean and standard deviation)
- Confidence calibration evaluation using reliability diagrams
- Error analysis including confusion matrix examination
- Statistical significance testing using paired t-tests

3.8.2 Validation Procedures

**Internal Validation:**
- 10-fold stratified cross-validation for robust performance estimation
- Bootstrap sampling for confidence interval estimation
- Leave-one-out validation for small dataset assessment
- Temporal validation using time-based data splits

**External Validation:**
- Independent test set evaluation (20% of total data)
- Cross-domain validation across different content types
- Generalization testing on unseen AI generation models
- Real-world deployment validation in authentic scenarios

3.9 Implementation Technologies and Tools
The researchers applied a research instrument based on the Likert scale. This standard gave a clear plan for how the researcher created and managed their survey. It made sure that the questions and answer choices were made in a way that was dependable and stayed the same throughout. The directions below are the following procedure that is used to collect data
Step 1. The first thing to do is the picking of the main respondent of the research, the 50 (fifty) respondents that are using social media platforms actively, and also explaining how the system works and performs.
Step 2. After collecting data, the researcher examined the information to understand how the respondents understood the system's overall suitability, compatibility, usability, and security.
I.	The information in the survey form consists of the Email and classification of whether they know about Artificial Intelligence works.
II.	The survey consists of instructions on how to answer the survey by simply clicking the corresponding answer of the respondents in terms of awareness, benefits of AI, and AI generating false information,
III.	The last segment illustrates the feedback or recommendation from the respondents that the proponents gathered during the study.


Data Gathering Procedure
	The data gathering procedure using a Likert scale in the proponents' research requires several key steps. Firstly, the researchers developed a set of research questions or statements that is relevant to the study’s objectives. By using a Likert scale itself, which typically ranged from 5 to 1, with anchor points indicating 5 - STRONGLY AGREE, 4 - AGREE, 3 - NEUTRAL, 2 - DISAGREE, 1- STRONGLY AGREE. Moreover, to gather information, the researchers sent the survey to the chosen respondents, using Google Form questionnaires. Participants were asked to choose the Likert scale response that most closely matched their opinions for each question or statement. After collecting the data, the researchers analyzed it statistically to understand the findings.


Statistical Treatment of Data
	Since the research instrument used the Likert scale, to spread the mean equally on the verbal interpretation of the data, the study used a weighted mean. Additionally, as it will be used to typically represent the responses. Furthermore, the weighted mean is calculated by adding up all of the data that was collected from respondents and dividing the result by the total number of respondents. To interpret the weighted mean, the following scale is used.
 

Figure 2.0 Weighted Mean

Range	Descriptive Rating
1.00 - 1.80	STRONGLY DISAGREE
1.81 - 2.60	DISAGREE
2.61 - 3.40	NEUTRAL
3.41 - 4.20	AGREE
4.21 - 5.00	STRONGLY AGREE

Table 1.0 Likert Scale, Mean and Verbal Interpretation
The interval in this scale is computed as:
Where 5 is the highest and 1 is the lowest number in Likert Scale used in this study.
Technical Requirements

A.	Hardware Requirements






Figure 3.0 DESKTOP/LAPTOP/MOBILE DEVICE

Desktop Application Users:
Processor: Intel Core i5 or equivalent AMD processor with at least 4 cores and a base clock speed of 2.5 GHz or higher.
Memory (RAM): Minimum 8 GB of RAM to ensure smooth performance, especially when handling large datasets and running complex algorithms.
Storage: Solid State Drive (SSD) with sufficient capacity for storing datasets, application files, and temporary processing data.
Graphics: Integrated or dedicated graphics card that supports the application's graphical user interface (GUI) requirements.
Operating System: Windows 10, macOS 10.14 (Mojave), or a recent version of a Linux distribution (e.g., Ubuntu 20.04 LTS).

Mobile Application Users:
Processor: For iOS devices, an Apple A12 Bionic chip or later. For Android devices, a Qualcomm Snapdragon 660 or equivalent.
Memory (RAM): Minimum 2 GB of RAM for iOS devices and 3 GB for Android devices to ensure smooth application performance.
Storage: Adequate storage space (at least 16 GB) for installing the application and storing necessary data.
Operating System: iOS 12 or later for iPhones and iPadOS for iPads.
Android 9.0 (Pie) or later for Android smartphones and tablets.
Web Application Users:
Processor: Any modern processor that supports the latest versions of web browsers like Google Chrome, Mozilla Firefox, Safari, or Microsoft Edge.
Memory (RAM): Minimum 4 GB of RAM for optimal performance, especially when handling multiple browser tabs or complex web applications.
Storage: Browser cache and temporary storage sufficient to handle web application data and session information.
Internet Connectivity: Stable broadband internet connection with a recommended minimum speed of 10 Mbps for seamless interaction with the web application.

B.	Software Requirements:
HTML






Figure 4.0 HTML
The HTML, or Hypertext Markup Language, is the code that is essential in creating a web page and its design on the internet. Consider it as a structure that provides order to a webpage. It uses special codes known as "tags" to indicate various page elements, including headings, paragraphs, images, links, and more. To put it simply, web browsers display websites on the internet by reading HTML.

CSS










Figure 5.0 CSS

The CSS stands for Cascading Style Sheets. It's a language used for describing the presentation or the look and formatting of a document written in HTML or XML (including XML dialects like SVG or XHTML).



PYTHON










Figure 6.0 PYTHON
Python's design philosophy emphasizes code readability and simplicity, which makes it accessible for beginners while still powerful enough for experienced developers. It features a large standard library and supports multiple programming paradigms, including procedural, object-oriented, and functional programming styles.
PHP







Figure 7.0 PHP

PHP is a widely used open-source server -side scripting language that is especially suited for web development. All in all PHP is a powerful tool for creating dynamic and interactive web pages and is widely used across the internet for building websites, web applications, and other web-based systems.







MYSQL








Figure 8.0 MYSQL
MySQL is an open-source relational database management system. As with other relational databases, MySQL stores data in tables made up of rows and columns. Users can define, manipulate, control, and query data using Structured Query Language, more commonly known as SQL.

C.	Network Requirement







Figure 9.0 Internet Network

Bandwidth Requirements
Data transfer: 
The amount of data use to be transferred per request can be vary depending on the sizes of the input and output texts, for typical text-based interactions, this might be relatively, but if your inputs involves frequent and large data exchanges a higher bandwidth will be more beneficial
Low latency: 
This system typically requires low-latency connections to provide real-time or near real-time responses. Where a high speed on internet connection is recommended for minimizing delays

D.	Project Design and Sample Code













Figure 10.0 Login Page and Sign-up Page

When opening the web-application the user will be redirected to the login page where users are able to create their own personal account to gain access if they don’t have one. After creating an account, they will be redirected back to the login page in order for them to proceed to the home page where they can find information about the goals, status of this project and access to its services.



Figure 11.0 Home Page
The image shown above is the home page where users will be redirected if they successfully login their account. Inside the home page, the users who gain access thru login and registeration they can navigate and read the information about the researchers goals, news, and the plans for the system including this research paper where future researchers can use this as a reference of their personal study in order to combat false generated content materials and unauthentic articles.







Figure 12.0 Service Page
The service page serves as the central main feature of this system project, allowing users to actively engage with the AI text detection tool. This where users input gathered content throughout the social media platforms as they may have been generated by artificial intelligence, particularly in contexts that is associated with common online misinformation, trolls fake news articles and etc..
Diagrams

A.	System Architecture
	It refers to the development of structured framework that is used to conceptualize and organize the components of a system and their interactions. It also outlines how different parts work together to achieve specific goals, ensuring that they function efficiently and effectively for the users of this system .









Figure 13.0 System Architecture

The image above shows the web-based application underlying system architecture. The diagram shows the graphical representation of the pattern that is needed for the software system physical implementation. The user can access the web application on any kind of device because the system is cross-platform, allowing the user to use any kind of device.

B.	Data Flow Diagram
Context Diagram








Figure 14.0 Context Diagram
The User is a person who interacts with the system to create an account, view the home page, upload articles, and receive the AI-detection result if their articles are AI-Generated or not. The AI-Text Article Detector is the core of the system. It receives articles from the User, analyzes them, and generates the appropriate text to inform the users. The Admin is responsible for updating the AI model, viewing AI detection results, and managing the user interface to be more efficient for users.


Data Flow Diagram









Figure 15.0 Data Flow Diagram
The data flow diagram used to illustrate how a developer controls the online application and user interaction is shown in the image. In addition, it offers a full view of the data flow inside the AI-Text Article Detector system, such us; users upload files, can view creators’ information, see results and admin updating the system.


C.	Proposed Flow Chart













Figure 16.0 Proposed Flow Chart
The figure above is a flow chart that shows the interaction of the user to the web-application from login, creating an account so they can access the service, and uploading text/file to detect if the article is AI-Generated. In addition, the users also can access the information of the team and they can read the study of the web- application developers.

                              System Development Methodology
In order to successfully build and deploy this system project AI-Text Detection System, the researchers adopted a prototype-based development process. This approach allows the researcher to create a functional version of the system throughout the early stage and continuously refine it through with user’s feedback, testing and future enhancement of modern technologies. The prototype method was chosen due to the nature of AI-integrated systems that require real-time testing with visual feedback and adjustments based on how users interact with its features. With each component such as login systems, AI detection backends including its result display were developed and tested one after another before being merged into one unified platform. 
The process followed by these key phases:
1.	Planning and Data Gathering - Determined what users needs and what features the system should include. (ex, login, file upload, accurate analysis).
2.	Prototype Design - Built initial project design using tools such as HTML/CSS and created a backend using Python and PHP.
3.	Integration and Testing - Connected the frontend to backend via local host/server (using XAMPP) and tested AI detection through it’s script with it’s live API calls 
4.	Evaluation and Feedback - Let users determine the capabilities of the system in order for them to provide reliable opinion for further enhancement of the system and to fix possible errors.
5.	Final Refinement - Adjusted Ui elements to modern user friendly elements, added security features and database logging based on the developer ideas and testers feedback received.
Algorithm Discussion
Inspired by the human brain, neural networks are like intricate webs of algorithms. These algorithms work together to sift through data, uncovering hidden patterns and connections. Think of it as the network learning on its own, constantly refining its approach based on new information. This makes them incredibly versatile, able to adapt to changing situations without needing a complete overhaul.

NLP stands for Natural Language Processing. It's a field that's growing quickly and combines computer science, artificial intelligence, and the study of language. NLP basically teaches computers how to understand, interpret, and even create human language. This is useful because the amount of text data we create keeps exploding, from social media to research papers. NLP tools can sift through this data to find important information and automate all sorts of tasks.

The GPT algorithm has been trained with vast amounts of text gathered from the internet. By reading so much, it learned the patterns of language, like grammar and sentence structure. It's like learning a language by reading tons of books, but way faster. This lets the machine predict what words are likely to come next, like how you might guess the next word in a familiar sentence. Interestingly, the machine doesn't just learn the words themselves, but also the ideas and knowledge hidden within those words. It's like it can understand the meaning behind the words, even though nobody explicitly taught it

The Neural Network Classification, while not yet fully applied to the current version of the system, the developer is envisioned on training and developing a custom neural network using semi-supervised datasets composed of labeled and unlabeled samples between Human Authentic Written Works and AI-Generated Text and false articles. where the use of semi-supervised learning allows the system to scan even with limited datasets provided as if it can learn from a combination of inferred patterns and known samples. 


Features:
1.	Text Detection
2.	AI Generated
3.	User Friendly
4.	Fraud Content Detection
5.	Secure Platform
6.	Shows Detection Result
7.	Have Multiple Programming Languages

Function
1.	Allows the user to know if the text is fraudulent in social media platforms by using AI- Text Article Detector.
2.	Users will	enter the suspected article then the system	will determine whether it’s AI-generated or not.
3.	Display the result of the AI-Text Article Detector.
4.	If the Article is not AI-generated it will inform the user that the article is not generated by an AI.
5.	If the Article is generated by AI the system will detect it and display on the screen that the article is made by an AI.
6.	The user can use the detector if they want to use it anytime.

USE
	The algorithm and its function that identify above was an important part of an AI-detection that the researchers developed because they help to guarantee that the detection is accurate and working properly together.

