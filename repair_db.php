<?php
echo "🔧 Repairing Database\n";
echo "====================\n\n";

try {
    $conn = new mysqli("localhost", "root", "", "mydbthesis", 3307);
    
    if ($conn->connect_error) {
        echo "❌ Connection failed: " . $conn->connect_error . "\n";
        exit(1);
    }
    
    echo "✅ Connected to database\n";
    
    // Drop and recreate users table
    echo "\n1. Dropping existing users table...\n";
    if ($conn->query("DROP TABLE IF EXISTS users")) {
        echo "✅ Users table dropped\n";
    } else {
        echo "❌ Error dropping users table: " . $conn->error . "\n";
    }
    
    // Create fresh users table
    echo "\n2. Creating fresh users table...\n";
    $userTableSql = "CREATE TABLE users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        reset_token VARCHAR(255) NULL,
        reset_expires DATETIME NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    if ($conn->query($userTableSql)) {
        echo "✅ Users table created successfully\n";
    } else {
        echo "❌ Error creating users table: " . $conn->error . "\n";
        exit(1);
    }
    
    // Test the table
    echo "\n3. Testing users table...\n";
    $result = $conn->query("SELECT COUNT(*) as count FROM users");
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        echo "✅ Users table working - count: $count\n";
    } else {
        echo "❌ Error testing users table: " . $conn->error . "\n";
    }
    
    // Create/verify feedback table
    echo "\n4. Creating/verifying feedback table...\n";
    $feedbackTableSql = "CREATE TABLE IF NOT EXISTS feedback (
        id INT AUTO_INCREMENT PRIMARY KEY,
        feedback_text TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    if ($conn->query($feedbackTableSql)) {
        echo "✅ Feedback table ready\n";
    } else {
        echo "❌ Error with feedback table: " . $conn->error . "\n";
    }
    
    // Test insert/select
    echo "\n5. Testing database operations...\n";
    
    // Insert test user
    $testUser = 'admin';
    $testEmail = '<EMAIL>';
    $testPass = password_hash('admin123', PASSWORD_DEFAULT);
    
    $stmt = $conn->prepare("INSERT INTO users (username, email, password) VALUES (?, ?, ?)");
    $stmt->bind_param("sss", $testUser, $testEmail, $testPass);
    
    if ($stmt->execute()) {
        echo "✅ Test user created: admin/admin123\n";
    } else {
        echo "❌ Error creating test user: " . $stmt->error . "\n";
    }
    
    // Verify user exists
    $result = $conn->query("SELECT username FROM users WHERE username = 'admin'");
    if ($result && $result->num_rows > 0) {
        echo "✅ Test user verified in database\n";
    } else {
        echo "❌ Test user not found\n";
    }
    
    echo "\n" . str_repeat("=", 40) . "\n";
    echo "🎉 DATABASE REPAIR COMPLETE!\n";
    echo str_repeat("=", 40) . "\n";
    echo "✅ Database is now ready for use\n";
    echo "\nTest credentials:\n";
    echo "Username: admin\n";
    echo "Password: admin123\n";
    echo "\nAccess the app at:\n";
    echo "http://localhost/my_freakin_thesis/index.php\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
